from django.shortcuts import render
from django.db import models
from .models import SupplyItem, SupplyRequest, SupplyCategory, UserProfile

def home(request):
    """Landing page view - shows welcome page for all users"""
    # If user is not authenticated, show landing page with limited info
    if not request.user.is_authenticated:
        context = {
            'show_stats': False,
            'total_items': SupplyItem.objects.count(),
            'total_categories': SupplyCategory.objects.count(),
        }
        return render(request, 'landing.html', context)

    # For authenticated users, show full landing page with stats
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        # Create default profile for existing users
        profile = UserProfile.objects.create(
            user=request.user,
            role='department_user'
        )

    # Get statistics based on user role
    context = {
        'show_stats': True,
        'user_profile': profile,
        'total_items': SupplyItem.objects.count(),
        'pending_requests': SupplyRequest.objects.filter(status='pending').count(),
        'low_stock_items': SupplyItem.objects.filter(current_stock__lte=models.F('minimum_stock')).count(),
        'total_categories': SupplyCategory.objects.count(),
    }

    return render(request, 'landing.html', context)
