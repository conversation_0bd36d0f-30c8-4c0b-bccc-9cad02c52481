{% extends 'base.html' %}
{% load static %}

{% block title %}Smart Supply Management System - Welcome{% endblock %}

{% block page_title %}Welcome to Smart Supply{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-gradient text-white rounded-2xl p-8 mb-8 relative overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-10"></div>
    <div class="relative z-10">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 slide-in-left">
                Smart Supply Management
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90 slide-in-right">
                Streamline your inventory, track supplies with QR codes, and manage requests efficiently
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in-up">
                {% if user.is_authenticated %}
                    <a href="{% url 'dashboard' %}"
                       class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300 shadow-lg">
                        Go to Dashboard
                    </a>
                {% else %}
                    <a href="{% url 'login' %}"
                       class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300 shadow-lg">
                        Login
                    </a>
                    <a href="{% url 'register' %}"
                       class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                        Register
                    </a>
                {% endif %}
                <button onclick="scrollToFeatures()"
                        class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                    Learn More
                </button>
            </div>
        </div>
    </div>
    
    <!-- Floating Icons -->
    <div class="absolute top-10 right-10 floating-animation opacity-20">
        <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
        </svg>
    </div>
    <div class="absolute bottom-10 left-10 floating-animation opacity-20" style="animation-delay: 1s;">
        <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
    </div>
</div>

<!-- Quick Stats Section -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12" x-data="statsData()">
    <div class="stat-card rounded-xl p-6 text-center text-white bg-gradient-to-br from-blue-500 to-blue-600">
        <div class="text-3xl font-bold mb-2" x-text="totalItems">{{ total_items|default:0 }}</div>
        <div class="text-blue-100">Total Items</div>
        <div class="mt-2">
            <svg class="w-8 h-8 mx-auto opacity-80" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
            </svg>
        </div>
    </div>

    {% if user.is_authenticated %}
    <div class="stat-card rounded-xl p-6 text-center text-white bg-gradient-to-br from-green-500 to-green-600">
        <div class="text-3xl font-bold mb-2" x-text="pendingRequests">{{ pending_requests|default:0 }}</div>
        <div class="text-green-100">Pending Requests</div>
        <div class="mt-2">
            <svg class="w-8 h-8 mx-auto opacity-80" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </div>

    <div class="stat-card rounded-xl p-6 text-center text-white bg-gradient-to-br from-yellow-500 to-orange-500">
        <div class="text-3xl font-bold mb-2" x-text="lowStockItems">{{ low_stock_items|default:0 }}</div>
        <div class="text-yellow-100">Low Stock Items</div>
        <div class="mt-2">
            <svg class="w-8 h-8 mx-auto opacity-80 pulse-animation" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </div>
    {% else %}
    <div class="stat-card rounded-xl p-6 text-center text-white bg-gradient-to-br from-gray-400 to-gray-500">
        <div class="text-3xl font-bold mb-2">?</div>
        <div class="text-gray-100">Login to View</div>
        <div class="mt-2">
            <svg class="w-8 h-8 mx-auto opacity-80" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </div>

    <div class="stat-card rounded-xl p-6 text-center text-white bg-gradient-to-br from-gray-400 to-gray-500">
        <div class="text-3xl font-bold mb-2">?</div>
        <div class="text-gray-100">Login to View</div>
        <div class="mt-2">
            <svg class="w-8 h-8 mx-auto opacity-80" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </div>
    {% endif %}

    <div class="stat-card rounded-xl p-6 text-center text-white bg-gradient-to-br from-purple-500 to-purple-600">
        <div class="text-3xl font-bold mb-2" x-text="totalCategories">{{ total_categories|default:0 }}</div>
        <div class="text-purple-100">Categories</div>
        <div class="mt-2">
            <svg class="w-8 h-8 mx-auto opacity-80" fill="currentColor" viewBox="0 0 20 20">
                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path>
            </svg>
        </div>
    </div>
</div>

<!-- Features Section -->
<div id="features" class="mb-12">
    <h2 class="text-3xl font-bold text-center text-gray-800 mb-8">Key Features</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- QR Code Tracking -->
        <div class="feature-card bg-white rounded-xl p-6 shadow-lg border border-gray-100">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1V4a1 1 0 00-1-1h-3zm1 2v1h1V5h-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-3">QR Code Tracking</h3>
            <p class="text-gray-600 mb-4">Track every supply item with unique QR codes for efficient inventory management and real-time updates.</p>
            <div class="flex items-center text-blue-600 font-medium">
                <span>Learn more</span>
                <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </div>
        </div>

        <!-- Request Management -->
        <div class="feature-card bg-white rounded-xl p-6 shadow-lg border border-gray-100">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-3">Request Management</h3>
            <p class="text-gray-600 mb-4">Streamlined supply request workflow with approval processes and automated tracking from request to release.</p>
            <div class="flex items-center text-green-600 font-medium">
                <span>Learn more</span>
                <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </div>
        </div>

        <!-- Role-Based Access -->
        <div class="feature-card bg-white rounded-xl p-6 shadow-lg border border-gray-100">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 mb-3">Role-Based Access</h3>
            <p class="text-gray-600 mb-4">Secure access control with different permission levels for admins, GSO staff, and department users.</p>
            <div class="flex items-center text-purple-600 font-medium">
                <span>Learn more</span>
                <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Section -->
{% if user.is_authenticated %}
<div class="bg-white rounded-xl shadow-lg p-6 mb-8" x-data="recentActivity()">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Recent Activity</h2>
        <button @click="refreshActivity()"
                class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
            </svg>
        </button>
    </div>

    <div class="space-y-4" x-show="activities.length > 0">
        <template x-for="activity in activities" :key="activity.id">
            <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 rounded-full flex items-center justify-center"
                         :class="activity.type === 'request' ? 'bg-blue-100 text-blue-600' :
                                activity.type === 'scan' ? 'bg-green-100 text-green-600' :
                                'bg-purple-100 text-purple-600'">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" x-show="activity.type === 'request'">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" x-show="activity.type === 'scan'">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5z" clip-rule="evenodd"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" x-show="activity.type === 'inventory'">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900" x-text="activity.title"></p>
                    <p class="text-sm text-gray-500" x-text="activity.description"></p>
                    <p class="text-xs text-gray-400 mt-1" x-text="activity.time"></p>
                </div>
            </div>
        </template>
    </div>

    <div x-show="activities.length === 0" class="text-center py-8 text-gray-500">
        <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <p>No recent activity to display</p>
    </div>
</div>
{% endif %}

<!-- Quick Actions Section -->
{% if user.is_authenticated %}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    {% if user.userprofile.role == 'department_user' %}
    <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white">
        <h3 class="text-xl font-semibold mb-3">Create New Request</h3>
        <p class="text-blue-100 mb-4">Submit a new supply request for your department</p>
        <a href="{% url 'create_request' %}" class="inline-flex items-center bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
            New Request
        </a>
    </div>
    {% endif %}

    {% if user.userprofile.role == 'gso_staff' %}
    <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white">
        <h3 class="text-xl font-semibold mb-3">QR Scanner</h3>
        <p class="text-green-100 mb-4">Scan QR codes to track inventory movements</p>
        <a href="{% url 'qr_scanner' %}" class="inline-flex items-center bg-white text-green-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5z" clip-rule="evenodd"></path>
            </svg>
            Open Scanner
        </a>
    </div>
    {% endif %}

    {% if user.userprofile.role == 'admin' or user.userprofile.role == 'gso_staff' %}
    <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white">
        <h3 class="text-xl font-semibold mb-3">Inventory Management</h3>
        <p class="text-purple-100 mb-4">Manage stock levels and item information</p>
        <a href="{% url 'inventory_list' %}" class="inline-flex items-center bg-white text-purple-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6z"></path>
            </svg>
            Manage Inventory
        </a>
    </div>
    {% endif %}
</div>
{% else %}
<!-- Call to Action for Unauthenticated Users -->
<div class="bg-white rounded-xl shadow-lg p-8 mb-8 text-center">
    <h2 class="text-3xl font-bold text-gray-800 mb-4">Ready to Get Started?</h2>
    <p class="text-gray-600 mb-6">Join our Smart Supply Management System to streamline your inventory and supply requests.</p>
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{% url 'register' %}" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-300 shadow-lg">
            Create Account
        </a>
        <a href="{% url 'login' %}" class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300">
            Sign In
        </a>
    </div>
</div>
{% endif %}

<!-- System Status -->
<div class="bg-white rounded-xl shadow-lg p-6" x-data="systemStatus()">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">System Status</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                 :class="status.inventory === 'healthy' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6z"></path>
                </svg>
            </div>
            <h3 class="font-semibold text-gray-800">Inventory System</h3>
            <p class="text-sm text-gray-600 capitalize" x-text="status.inventory"></p>
        </div>

        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                 :class="status.requests === 'healthy' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="font-semibold text-gray-800">Request Processing</h3>
            <p class="text-sm text-gray-600 capitalize" x-text="status.requests"></p>
        </div>

        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                 :class="status.qr === 'healthy' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="font-semibold text-gray-800">QR System</h3>
            <p class="text-sm text-gray-600 capitalize" x-text="status.qr"></p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Smooth scroll to features section
    function scrollToFeatures() {
        document.getElementById('features').scrollIntoView({
            behavior: 'smooth'
        });
    }

    // Alpine.js data functions
    function statsData() {
        return {
            totalItems: {{ total_items|default:0 }},
            pendingRequests: {{ pending_requests|default:0 }},
            lowStockItems: {{ low_stock_items|default:0 }},
            totalCategories: {{ total_categories|default:0 }},

            init() {
                {% if user.is_authenticated %}
                this.loadStats();
                {% else %}
                // For unauthenticated users, just animate the available stats
                this.animateCounter('totalItems', {{ total_items|default:0 }});
                this.animateCounter('totalCategories', {{ total_categories|default:0 }});
                {% endif %}
            },

            async loadStats() {
                try {
                    // Animate counters with server data
                    this.animateCounter('totalItems', {{ total_items|default:0 }});
                    this.animateCounter('pendingRequests', {{ pending_requests|default:0 }});
                    this.animateCounter('lowStockItems', {{ low_stock_items|default:0 }});
                    this.animateCounter('totalCategories', {{ total_categories|default:0 }});
                } catch (error) {
                    console.error('Failed to load stats:', error);
                }
            },

            animateCounter(property, target) {
                const duration = 2000;
                const steps = 60;
                const increment = target / steps;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        this[property] = target;
                        clearInterval(timer);
                    } else {
                        this[property] = Math.floor(current);
                    }
                }, duration / steps);
            }
        }
    }

    function recentActivity() {
        return {
            activities: [],

            init() {
                this.loadActivities();
            },

            async loadActivities() {
                try {
                    // Simulate API call - replace with actual endpoint
                    await new Promise(resolve => setTimeout(resolve, 500));

                    this.activities = [
                        {
                            id: 1,
                            type: 'request',
                            title: 'New Supply Request',
                            description: 'REQ-20240128-0001 submitted by John Doe',
                            time: '2 minutes ago'
                        },
                        {
                            id: 2,
                            type: 'scan',
                            title: 'QR Code Scanned',
                            description: 'Office supplies scanned for inventory check',
                            time: '15 minutes ago'
                        },
                        {
                            id: 3,
                            type: 'inventory',
                            title: 'Stock Updated',
                            description: 'Printer paper stock increased by 50 units',
                            time: '1 hour ago'
                        },
                        {
                            id: 4,
                            type: 'request',
                            title: 'Request Approved',
                            description: 'REQ-20240127-0045 approved by GSO Staff',
                            time: '2 hours ago'
                        }
                    ];
                } catch (error) {
                    console.error('Failed to load activities:', error);
                }
            },

            async refreshActivity() {
                this.activities = [];
                await this.loadActivities();
            }
        }
    }

    function systemStatus() {
        return {
            status: {
                inventory: 'healthy',
                requests: 'healthy',
                qr: 'healthy'
            },

            init() {
                this.checkSystemStatus();
                // Check status every 30 seconds
                setInterval(() => {
                    this.checkSystemStatus();
                }, 30000);
            },

            async checkSystemStatus() {
                try {
                    // Simulate API call - replace with actual endpoint
                    await new Promise(resolve => setTimeout(resolve, 200));

                    // Simulate random status changes for demo
                    const statuses = ['healthy', 'warning', 'error'];
                    this.status = {
                        inventory: statuses[Math.floor(Math.random() * 3)],
                        requests: statuses[Math.floor(Math.random() * 3)],
                        qr: statuses[Math.floor(Math.random() * 3)]
                    };
                } catch (error) {
                    console.error('Failed to check system status:', error);
                }
            }
        }
    }

    // Initialize page animations
    document.addEventListener('DOMContentLoaded', function() {
        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            observer.observe(card);
        });
    });
</script>
{% endblock %}
